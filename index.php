<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

$pageTitle = 'Home - Discover Amazing Events';

// Check if this is an API request
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = trim($path, '/');

if (strpos($path, 'api') === 0) {
    // Handle API requests
    header('Content-Type: application/json');

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($path) {
        case 'api':
        case 'api/':
            echo json_encode([
                'message' => 'Event Booking System API',
                'version' => '2.0.0',
                'endpoints' => [
                    'GET /api/events' => 'Get all events',
                    'GET /api/events/{id}' => 'Get specific event',
                    'POST /api/events' => 'Create new event (admin only)',
                    'PUT /api/events/{id}' => 'Update event (admin only)',
                    'DELETE /api/events/{id}' => 'Delete event (admin only)',
                    'GET /api/bookings' => 'Get all bookings (admin only)',
                    'POST /api/bookings' => 'Create new booking',
                    'GET /api/users' => 'Get all users (admin only)',
                    'POST /api/auth/login' => 'User authentication',
                    'POST /api/auth/register' => 'User registration',
                    'GET /api/stats' => 'System statistics (admin only)'
                ],
                'database_status' => testDatabaseConnection() ? 'connected' : 'disconnected'
            ]);
            break;

        case 'api/events':
            handleEventsAPI($method);
            break;

        case 'api/bookings':
            handleBookingsAPI($method);
            break;

        case 'api/users':
            handleUsersAPI($method);
            break;

        case 'api/auth/login':
            handleLoginAPI($method);
            break;

        case 'api/auth/register':
            handleRegisterAPI($method);
            break;

        case 'api/stats':
            handleStatsAPI($method);
            break;

        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
    exit; // Exit for API requests
}

// Frontend - Get featured events for homepage
$featuredEvents = $eventManager->getFeaturedEvents(6);
$totalEvents = $eventManager->getTotalEventsCount();
$totalUsers = $userManager->getTotalUsersCount();
$totalBookings = $bookingManager->getTotalBookingsCount();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Discover and book amazing events in Central Africa. From concerts to conferences, find your next unforgettable experience.">
    <meta name="keywords" content="events, booking, tickets, concerts, conferences, Central Africa">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/modern-ui.css" rel="stylesheet">

    <!-- Meta tags for social sharing -->
    <meta property="og:title" content="<?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?>">
    <meta property="og:description" content="Discover and book amazing events in Central Africa">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-modern fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-calendar-star me-2"></i>
                EventHub
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/">Browse Events</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events/search.php">Search</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="booking/cart.php">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            <span class="cart-count badge bg-danger position-absolute top-0 start-100 translate-middle" style="display: none;">0</span>
                        </a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="user/dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="user/profile.php">Profile</a></li>
                                <?php if (isAdmin()): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="admin/">Admin Panel</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php">Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-modern ms-2" href="auth/register.php">Sign Up</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-modern">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content animate-on-scroll">
                        <h1 class="hero-title">
                            Discover Amazing
                            <span style="background: linear-gradient(45deg, #f093fb, #f5576c); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Events</span>
                        </h1>
                        <p class="hero-subtitle">
                            Book tickets for concerts, conferences, workshops, and more.
                            Experience unforgettable moments with EventHub - your gateway to extraordinary experiences.
                        </p>
                        <div class="hero-actions">
                            <a href="events/" class="btn btn-primary-modern me-3">
                                <i class="fas fa-search me-2"></i>
                                Explore Events
                            </a>
                            <a href="#featured" class="btn btn-secondary-modern">
                                <i class="fas fa-play me-2"></i>
                                Watch Demo
                            </a>
                        </div>

                        <!-- Stats -->
                        <div class="row mt-5">
                            <div class="col-4">
                                <div class="stat-item text-center">
                                    <h3 class="stat-number"><?php echo number_format($totalEvents); ?></h3>
                                    <p class="stat-label">Active Events</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item text-center">
                                    <h3 class="stat-number"><?php echo number_format($totalUsers); ?></h3>
                                    <p class="stat-label">Happy Users</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item text-center">
                                    <h3 class="stat-number"><?php echo number_format($totalBookings); ?></h3>
                                    <p class="stat-label">Bookings</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image animate-on-scroll">
                        <div class="glass-container p-4">
                            <img src="https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                                 alt="Event Experience" class="img-fluid rounded-3">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Section -->
    <section class="py-5" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px);">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="search-container glass-container p-4">
                        <h3 class="text-center mb-4 text-white">Find Your Perfect Event</h3>
                        <form class="row g-3" action="events/search.php" method="GET">
                            <div class="col-md-4">
                                <input type="text" class="form-control form-control-modern"
                                       name="search" placeholder="Search events..." id="eventSearch">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control form-control-modern" name="location">
                                    <option value="">All Locations</option>
                                    <option value="Douala">Douala</option>
                                    <option value="Yaoundé">Yaoundé</option>
                                    <option value="Libreville">Libreville</option>
                                    <option value="Bangui">Bangui</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control form-control-modern" name="date">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary-modern w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Events Section -->
    <section id="featured" class="py-5" style="background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-4 fw-bold text-white animate-on-scroll">Featured Events</h2>
                    <p class="lead text-white-50 animate-on-scroll">Don't miss these amazing experiences</p>
                </div>
            </div>

            <!-- Category Filters -->
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <div class="filter-buttons">
                        <button class="btn btn-secondary-modern filter-btn active me-2 mb-2" data-filter="all">All Events</button>
                        <button class="btn btn-secondary-modern filter-btn me-2 mb-2" data-filter="Technology">Technology</button>
                        <button class="btn btn-secondary-modern filter-btn me-2 mb-2" data-filter="Music">Music</button>
                        <button class="btn btn-secondary-modern filter-btn me-2 mb-2" data-filter="Business">Business</button>
                        <button class="btn btn-secondary-modern filter-btn me-2 mb-2" data-filter="Art">Art</button>
                        <button class="btn btn-secondary-modern filter-btn me-2 mb-2" data-filter="Sports">Sports</button>
                    </div>
                </div>
            </div>

            <div class="row events-container">
                <?php if (!empty($featuredEvents)): ?>
                    <?php foreach ($featuredEvents as $index => $event): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="event-card animate-on-scroll"
                                 data-event-id="<?php echo $event->id; ?>"
                                 data-category="<?php echo $event->category; ?>"
                                 style="animation-delay: <?php echo $index * 0.1; ?>s;">

                                <div class="event-image-container position-relative">
                                    <img src="<?php echo $event->image_url ?: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'; ?>"
                                         alt="<?php echo htmlspecialchars($event->title); ?>"
                                         class="event-image">

                                    <div class="event-badge">
                                        <?php echo htmlspecialchars($event->category); ?>
                                    </div>

                                    <?php if ($event->available_tickets < 10): ?>
                                        <div class="position-absolute top-0 start-0 m-3">
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-fire me-1"></i>Almost Sold Out
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="event-content">
                                    <h5 class="event-title"><?php echo htmlspecialchars($event->title); ?></h5>

                                    <div class="event-meta mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-calendar-alt text-primary me-2"></i>
                                            <span><?php echo formatDate($event->event_date); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-clock text-primary me-2"></i>
                                            <span><?php echo formatTime($event->event_time); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                            <span><?php echo htmlspecialchars($event->venue . ', ' . $event->location); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-ticket-alt text-primary me-2"></i>
                                            <span class="ticket-count"><?php echo $event->available_tickets; ?></span>
                                            <span class="text-muted ms-1">tickets left</span>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="event-price">
                                            <?php echo formatCurrency($event->price); ?>
                                        </div>
                                        <div class="event-actions">
                                            <a href="events/details.php?id=<?php echo $event->id; ?>"
                                               class="btn btn-outline-primary btn-sm me-2">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-primary-modern btn-sm add-to-cart-btn">
                                                <i class="fas fa-cart-plus me-1"></i>
                                                Add to Cart
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-white-50 mb-3"></i>
                        <h4 class="text-white">No Featured Events</h4>
                        <p class="text-white-50">Check back soon for exciting events!</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="row mt-5">
                <div class="col-12 text-center">
                    <a href="events/" class="btn btn-primary-modern btn-lg">
                        <i class="fas fa-calendar-alt me-2"></i>
                        View All Events
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px);">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="display-5 fw-bold animate-on-scroll">Why Choose EventHub?</h2>
                    <p class="lead text-muted animate-on-scroll">Experience the future of event booking</p>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100 animate-on-scroll">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-search fa-3x" style="background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h5 class="fw-bold">Smart Discovery</h5>
                        <p class="text-muted">AI-powered search with intelligent filtering to find your perfect event.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100 animate-on-scroll">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-shield-alt fa-3x" style="background: var(--success-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h5 class="fw-bold">Secure Booking</h5>
                        <p class="text-muted">Bank-level security with encrypted payments and instant confirmations.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100 animate-on-scroll">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-mobile-alt fa-3x" style="background: var(--warning-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h5 class="fw-bold">Mobile Experience</h5>
                        <p class="text-muted">Responsive design with offline support and native app-like experience.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100 animate-on-scroll">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-chart-line fa-3x" style="background: var(--danger-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h5 class="fw-bold">Real-time Updates</h5>
                        <p class="text-muted">Live ticket availability and instant booking confirmations.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100 animate-on-scroll">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-headset fa-3x" style="background: var(--secondary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h5 class="fw-bold">24/7 Support</h5>
                        <p class="text-muted">Round-the-clock customer support for all your event needs.</p>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card-modern text-center p-4 h-100 animate-on-scroll">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-star fa-3x" style="background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h5 class="fw-bold">Premium Experience</h5>
                        <p class="text-muted">Curated events and exclusive access to premium experiences.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-5" style="background: var(--dark-bg); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-calendar-star me-2"></i>
                        EventHub
                    </h5>
                    <p class="text-muted">Modern event booking platform for Central Africa. Discover, book, and experience amazing events with cutting-edge technology.</p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram fa-lg"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-linkedin fa-lg"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Platform</h6>
                    <ul class="list-unstyled">
                        <li><a href="events/" class="text-muted text-decoration-none">Browse Events</a></li>
                        <li><a href="events/search.php" class="text-muted text-decoration-none">Search</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Categories</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Venues</a></li>
                    </ul>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">Help Center</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Contact Us</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Privacy Policy</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Stay Updated</h6>
                    <p class="text-muted">Subscribe to get notified about new events and exclusive offers.</p>
                    <form class="d-flex">
                        <input type="email" class="form-control me-2" placeholder="Enter your email">
                        <button class="btn btn-primary-modern" type="submit">Subscribe</button>
                    </form>
                </div>
            </div>

            <hr class="my-4" style="border-color: rgba(255,255,255,0.1);">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 EventHub. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Made with ❤️ for Central Africa</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/modern-app.js"></script>

    <!-- Initialize animations -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add stagger animation delays
            document.querySelectorAll('.animate-on-scroll').forEach((el, index) => {
                el.style.setProperty('--stagger-delay', index);
            });
        });
    </script>
</body>
</html>

<?php
// API functions moved to the end
function handleEventsAPI($method) {
    global $eventManager;

    switch ($method) {
        case 'GET':
            $events = $eventManager->getAllEvents();
            echo json_encode(['success' => true, 'data' => $events]);
            break;

        case 'POST':
            // Require admin authentication
            if (!isAdmin()) {
                http_response_code(401);
                echo json_encode(['error' => 'Admin access required']);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);
            if ($eventManager->addEvent($input)) {
                echo json_encode(['success' => true, 'message' => 'Event created']);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to create event']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
}

function handleBookingsAPI($method) {
    global $bookingManager, $db;

    switch ($method) {
        case 'GET':
            if (!isAdmin()) {
                http_response_code(401);
                echo json_encode(['error' => 'Admin access required']);
                return;
            }

            $db->query('SELECT * FROM bookings ORDER BY created_at DESC');
            $bookings = $db->resultset();
            echo json_encode(['success' => true, 'data' => $bookings]);
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            $result = $bookingManager->createBooking($input);
            if ($result) {
                echo json_encode(['success' => true, 'booking_reference' => $result]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to create booking']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
}

function handleUsersAPI($method) {
    global $db;

    if (!isAdmin()) {
        http_response_code(401);
        echo json_encode(['error' => 'Admin access required']);
        return;
    }

    switch ($method) {
        case 'GET':
            $db->query('SELECT id, username, first_name, last_name, email, role, created_at FROM users ORDER BY created_at DESC');
            $users = $db->resultset();
            echo json_encode(['success' => true, 'data' => $users]);
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
}

function handleLoginAPI($method) {
    global $userManager;

    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';

    if ($userManager->login($username, $password)) {
        echo json_encode(['success' => true, 'message' => 'Login successful']);
    } else {
        http_response_code(401);
        echo json_encode(['error' => 'Invalid credentials']);
    }
}

function handleRegisterAPI($method) {
    global $userManager;

    if ($method !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if ($userManager->register($input)) {
        echo json_encode(['success' => true, 'message' => 'Registration successful']);
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Registration failed']);
    }
}

function handleStatsAPI($method) {
    global $db;

    if (!isAdmin()) {
        http_response_code(401);
        echo json_encode(['error' => 'Admin access required']);
        return;
    }

    if ($method !== 'GET') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }

    // Get system statistics
    $db->query('SELECT
        (SELECT COUNT(*) FROM events WHERE status = "active") as total_events,
        (SELECT COUNT(*) FROM users WHERE role = "user") as total_users,
        (SELECT COUNT(*) FROM bookings) as total_bookings,
        (SELECT SUM(total_amount) FROM bookings WHERE booking_status = "confirmed") as total_revenue
    ');
    $stats = $db->single();

    echo json_encode(['success' => true, 'data' => $stats]);
}

function testDatabaseConnection() {
    global $db;
    try {
        $db->query('SELECT 1');
        return true;
    } catch (Exception $e) {
        return false;
    }
}


?>
