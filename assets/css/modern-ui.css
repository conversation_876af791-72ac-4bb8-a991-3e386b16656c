/* Modern Event Booking System - Premium UI Framework */

:root {
    /* Color Palette - Modern Gradient System */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

    /* Glassmorphism Colors */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Dark Theme */
    --dark-bg: #0f0f23;
    --dark-surface: #1a1a2e;
    --dark-card: #16213e;
    --dark-text: #e94560;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-display: 'Poppins', sans-serif;

    /* Spacing & Sizing */
    --border-radius: 16px;
    --border-radius-lg: 24px;
    --shadow-soft: 0 10px 40px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 20px 60px rgba(0, 0, 0, 0.15);
    --shadow-strong: 0 30px 80px rgba(0, 0, 0, 0.2);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Glassmorphism Container */
.glass-container {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--glass-shadow);
}

/* Modern Navigation */
.navbar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition-smooth);
}

.navbar-brand {
    font-family: var(--font-display);
    font-weight: 700;
    font-size: 1.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link {
    font-weight: 500;
    color: #333 !important;
    transition: var(--transition-smooth);
    position: relative;
    padding: 0.5rem 1rem !important;
}

.nav-link:hover {
    color: #667eea !important;
    transform: translateY(-2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: var(--transition-smooth);
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 80%;
}

/* Hero Section */
.hero-modern {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--primary-gradient);
    position: relative;
    overflow: hidden;
}

.hero-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
}

.hero-title {
    font-family: var(--font-display);
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: slideInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: slideInUp 1s ease-out 0.2s both;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modern Buttons */
.btn-modern {
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    overflow: hidden;
    transition: var(--transition-smooth);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary-modern {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-soft);
}

.btn-primary-modern:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-secondary-modern {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary-modern:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    color: white;
}

/* Card System */
.card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    overflow: hidden;
    position: relative;
}

.card-modern:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-strong);
}

.card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

/* Event Cards */
.event-card {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-lg);
    background: white;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    cursor: pointer;
}

.event-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-strong);
}

.event-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.event-card:hover .event-image {
    transform: scale(1.1);
}

.event-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--secondary-gradient);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.event-content {
    padding: 1.5rem;
}

.event-title {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #333;
}

.event-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    color: #666;
    font-size: 0.9rem;
}

.event-price {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Form Styling */
.form-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.form-control-modern {
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    padding: 1rem;
    font-size: 1rem;
    transition: var(--transition-smooth);
    background: rgba(255, 255, 255, 0.8);
}

.form-control-modern:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
    outline: none;
}

/* Loading Animation */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Advanced Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Hover Effects */
.card-modern:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    z-index: 10000;
    border-radius: var(--border-radius);
    backdrop-filter: blur(20px);
    animation: slideInRight 0.3s ease-out;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Progress Indicators */
.progress-modern {
    height: 8px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.progress-modern .progress-bar {
    background: var(--primary-gradient);
    border-radius: 50px;
    transition: width 0.6s ease;
}

/* Advanced Form Styling */
.form-floating-modern {
    position: relative;
}

.form-floating-modern .form-control {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.form-floating-modern label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}

/* Table Enhancements */
.table-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-soft);
}

.table-modern th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.table-modern td {
    border: none;
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table-modern tbody tr {
    transition: var(--transition-smooth);
}

.table-modern tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* Badge Enhancements */
.badge-modern {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
}

/* Tooltip Styling */
.tooltip-modern {
    background: var(--dark-bg);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.85rem;
    box-shadow: var(--shadow-medium);
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(0, 0, 0, 0.2);
        --glass-border: rgba(255, 255, 255, 0.1);
    }

    body {
        background: var(--dark-bg);
        color: #e0e0e0;
    }

    .card-modern {
        background: rgba(0, 0, 0, 0.3);
        color: #e0e0e0;
    }

    .navbar-modern {
        background: rgba(0, 0, 0, 0.9);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn-modern {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .event-card {
        margin-bottom: 1.5rem;
    }

    .form-modern {
        padding: 1.5rem;
    }

    .notification {
        min-width: 280px;
        right: 10px;
        left: 10px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .display-5 {
        font-size: 2rem;
    }

    .card-modern {
        margin-bottom: 1rem;
    }
}

/* Print Styles */
@media print {
    .navbar-modern,
    .btn-modern,
    .notification {
        display: none !important;
    }

    .card-modern {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    body {
        background: white !important;
        color: black !important;
    }
}
